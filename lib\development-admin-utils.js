/**
 * Development Admin Utilities
 * 
 * Helper functions for handling development admin user in auth bypass mode
 * to avoid foreign key constraint violations when the development admin UUID
 * doesn't exist in the auth.users table.
 */

// Development admin UUID used in auth bypass mode
export const DEVELOPMENT_ADMIN_UUID = '00000000-0000-4000-8000-000000000001'

/**
 * Check if the given user ID is the development admin
 * @param {string} userId - The user ID to check
 * @returns {boolean} True if this is the development admin
 */
export function isDevelopmentAdmin(userId) {
  return userId === DEVELOPMENT_ADMIN_UUID
}

/**
 * Safely add created_by field to data object, skipping for development admin
 * to avoid foreign key constraint violations
 * 
 * @param {Object} data - The data object to modify
 * @param {string} adminUserId - The admin user ID
 * @param {string} [requestId] - Optional request ID for logging
 * @returns {Object} The modified data object
 */
export function safelyAddCreatedBy(data, adminUserId, requestId = '') {
  if (!isDevelopmentAdmin(adminUserId)) {
    data.created_by = adminUserId
  } else {
    if (requestId) {
      console.log(`[${requestId}] Skipping created_by field for development admin to avoid foreign key constraint`)
    } else {
      console.log('Skipping created_by field for development admin to avoid foreign key constraint')
    }
  }
  return data
}

/**
 * Safely add deactivated_by field to data object, skipping for development admin
 * to avoid foreign key constraint violations
 * 
 * @param {Object} data - The data object to modify
 * @param {string} adminUserId - The admin user ID
 * @param {string} [requestId] - Optional request ID for logging
 * @returns {Object} The modified data object
 */
export function safelyAddDeactivatedBy(data, adminUserId, requestId = '') {
  if (!isDevelopmentAdmin(adminUserId)) {
    data.deactivated_by = adminUserId
  } else {
    if (requestId) {
      console.log(`[${requestId}] Skipping deactivated_by field for development admin to avoid foreign key constraint`)
    } else {
      console.log('Skipping deactivated_by field for development admin to avoid foreign key constraint')
    }
  }
  return data
}

/**
 * Prepare token data with safe created_by handling
 * 
 * @param {Object} baseData - Base token data
 * @param {string} adminUserId - The admin user ID
 * @param {string} [requestId] - Optional request ID for logging
 * @returns {Object} Token data with safely handled created_by field
 */
export function prepareTokenData(baseData, adminUserId, requestId = '') {
  const tokenData = { ...baseData }
  return safelyAddCreatedBy(tokenData, adminUserId, requestId)
}

/**
 * Check if we're in development mode with auth bypass enabled
 * @returns {boolean} True if development auth bypass is active
 */
export function isDevelopmentAuthBypass() {
  return process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true'
}

/**
 * Get a safe admin user ID for database operations
 * Returns null for development admin to avoid foreign key issues
 * 
 * @param {string} adminUserId - The admin user ID
 * @returns {string|null} The admin user ID or null if development admin
 */
export function getSafeAdminUserId(adminUserId) {
  return isDevelopmentAdmin(adminUserId) ? null : adminUserId
}
