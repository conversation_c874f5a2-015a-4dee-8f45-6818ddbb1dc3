import { useState } from 'react'
import Head from 'next/head'
import styles from '@/styles/admin/Login.module.css'

export default function DebugAuth() {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState(null)
  const [error, setError] = useState('')

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const runDebugTest = async (action) => {
    setLoading(true)
    setError('')
    setResults(null)

    try {
      const response = await fetch('/api/debug/staff-login-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
          action
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setResults(data)
      } else {
        setError(data.error || 'Test failed')
        setResults(data)
      }
    } catch (err) {
      setError('Network error: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      <Head>
        <title>Authentication Debug Tool - Ocean Soul Sparkles</title>
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className={styles.loginContainer}>
        <div className={styles.loginCard}>
          <h1 className={styles.title}>🔧 Authentication Debug Tool</h1>
          <p style={{ color: '#666', marginBottom: '20px' }}>
            This tool helps debug Artist/Braider staff login issues.
          </p>

          <form className={styles.form}>
            <div className={styles.inputGroup}>
              <label htmlFor="email">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter the email address having login issues"
                className={styles.input}
              />
            </div>

            <div className={styles.inputGroup}>
              <label htmlFor="password">Password</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Enter the password"
                className={styles.input}
              />
            </div>

            <div style={{ display: 'flex', gap: '10px', marginTop: '20px' }}>
              <button
                type="button"
                onClick={() => runDebugTest('check_only')}
                disabled={loading || !formData.email || !formData.password}
                className={styles.submitButton}
                style={{ flex: 1 }}
              >
                {loading ? 'Checking...' : '🔍 Check User Status'}
              </button>

              <button
                type="button"
                onClick={() => runDebugTest('test_auth')}
                disabled={loading || !formData.email || !formData.password}
                className={styles.submitButton}
                style={{ flex: 1, backgroundColor: '#e74c3c' }}
              >
                {loading ? 'Testing...' : '🧪 Test Authentication'}
              </button>
            </div>
          </form>

          {error && (
            <div style={{ 
              backgroundColor: '#fee', 
              border: '1px solid #fcc', 
              padding: '15px', 
              borderRadius: '5px', 
              marginTop: '20px',
              color: '#c33'
            }}>
              <strong>Error:</strong> {error}
            </div>
          )}

          {results && (
            <div style={{ 
              backgroundColor: '#f9f9f9', 
              border: '1px solid #ddd', 
              padding: '15px', 
              borderRadius: '5px', 
              marginTop: '20px',
              fontSize: '14px'
            }}>
              <h3>Debug Results:</h3>
              
              {results.user && (
                <div style={{ marginBottom: '15px' }}>
                  <strong>User Summary:</strong>
                  <ul>
                    <li>ID: {results.user.id}</li>
                    <li>Email: {results.user.email}</li>
                    <li>Email Confirmed: {results.user.emailConfirmed ? 'Yes' : 'No'}</li>
                    <li>Role: {results.user.role || 'None'}</li>
                    <li>Application Status: {results.user.applicationStatus || 'N/A'}</li>
                  </ul>
                </div>
              )}

              {results.debugInfo && results.debugInfo.steps && (
                <div>
                  <strong>Debug Steps:</strong>
                  <div style={{ 
                    backgroundColor: '#000', 
                    color: '#0f0', 
                    padding: '10px', 
                    borderRadius: '3px', 
                    fontFamily: 'monospace',
                    fontSize: '12px',
                    maxHeight: '300px',
                    overflowY: 'auto'
                  }}>
                    {results.debugInfo.steps.map((step, index) => (
                      <div key={index}>{step}</div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          <div style={{ 
            marginTop: '30px', 
            padding: '15px', 
            backgroundColor: '#e8f4fd', 
            border: '1px solid #bee5eb', 
            borderRadius: '5px',
            fontSize: '14px'
          }}>
            <strong>How to use:</strong>
            <ol>
              <li>Enter the email and password of the user experiencing login issues</li>
              <li>Click "Check User Status" to see user account details</li>
              <li>Click "Test Authentication" to simulate the login process</li>
              <li>Review the debug output to identify the issue</li>
            </ol>
          </div>
        </div>
      </div>
    </>
  )
}
