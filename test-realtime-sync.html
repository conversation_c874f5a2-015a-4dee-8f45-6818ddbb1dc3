<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time POS Synchronization Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%); color: white; padding: 2rem; border-radius: 10px; margin-bottom: 2rem; }
        .test-panel { background: white; margin: 20px 0; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; border-left: 4px solid #28a745; padding: 15px; margin: 10px 0; }
        .error { background-color: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 10px 0; }
        .info { background-color: #d1ecf1; border-left: 4px solid #17a2b8; padding: 15px; margin: 10px 0; }
        .sync-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .mode-panel { background: #f8f9fa; padding: 20px; border-radius: 8px; border: 2px solid #dee2e6; }
        .mode-panel.full { border-color: #007bff; }
        .mode-panel.quick { border-color: #28a745; }
        .mode-title { font-size: 1.2rem; font-weight: bold; margin-bottom: 15px; text-align: center; }
        .service-count { font-size: 2rem; font-weight: bold; text-align: center; margin: 10px 0; }
        .service-count.full { color: #007bff; }
        .service-count.quick { color: #28a745; }
        .service-list { max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: white; font-size: 0.9rem; }
        .service-item { padding: 4px 0; border-bottom: 1px solid #eee; }
        .sync-status { padding: 15px; border-radius: 8px; margin: 15px 0; font-weight: bold; text-align: center; }
        .sync-status.synced { background: #d4edda; color: #155724; }
        .sync-status.out-of-sync { background: #f8d7da; color: #721c24; }
        button { background: #4ECDC4; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 1rem; margin: 5px; }
        button:hover { background: #44A08D; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .auto-refresh { display: flex; align-items: center; gap: 10px; margin: 15px 0; }
        .refresh-interval { padding: 5px; border: 1px solid #ddd; border-radius: 4px; }
        .timestamp { font-size: 0.8rem; color: #666; text-align: center; margin: 10px 0; }
        .loading { text-align: center; padding: 20px; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #4ECDC4; border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite; margin: 0 auto; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Real-time POS Synchronization Test</h1>
            <p>Monitor service synchronization between Full Booking Mode and Quick Event Mode in real-time</p>
        </div>

        <div class="test-panel">
            <h2>🎯 Test Controls</h2>
            <button onclick="runSyncTest()" id="testBtn">Run Sync Test</button>
            <button onclick="clearResults()">Clear Results</button>
            
            <div class="auto-refresh">
                <label>
                    <input type="checkbox" id="autoRefresh" onchange="toggleAutoRefresh()"> Auto-refresh every
                </label>
                <select class="refresh-interval" id="refreshInterval">
                    <option value="5000">5 seconds</option>
                    <option value="10000" selected>10 seconds</option>
                    <option value="30000">30 seconds</option>
                    <option value="60000">1 minute</option>
                </select>
            </div>
        </div>

        <div id="results"></div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let lastTestData = null;

        async function runSyncTest() {
            const testBtn = document.getElementById('testBtn');
            const originalText = testBtn.textContent;
            testBtn.disabled = true;
            testBtn.textContent = 'Testing...';

            try {
                showLoading();
                
                // Fetch services from API
                const response = await fetch('/api/admin/pos/services-with-artists');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const apiServices = data.services || [];
                
                // Simulate component filtering
                const fullBookingServices = [...apiServices]; // No filtering
                const quickEventServices = apiServices.filter(service => {
                    const visibleOnEvents = service.visible_on_events === true || service.visible_on_events === 'true';
                    const visibleOnPos = service.visible_on_pos === true || service.visible_on_pos === 'true';
                    const hasVisibilityFlags = service.visible_on_events !== undefined || service.visible_on_pos !== undefined;
                    return hasVisibilityFlags ? (visibleOnEvents || visibleOnPos) : true;
                });
                
                const testData = {
                    timestamp: new Date().toISOString(),
                    apiTotal: apiServices.length,
                    fullBookingCount: fullBookingServices.length,
                    quickEventCount: quickEventServices.length,
                    isSynced: fullBookingServices.length === quickEventServices.length,
                    fullBookingServices,
                    quickEventServices
                };
                
                lastTestData = testData;
                displaySyncResults(testData);
                
                hideLoading();
                
            } catch (error) {
                hideLoading();
                addResult(`❌ Test failed: ${error.message}`, 'error');
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = originalText;
            }
        }

        function displaySyncResults(data) {
            const syncStatus = data.isSynced ? 'synced' : 'out-of-sync';
            const syncMessage = data.isSynced 
                ? '✅ Perfect Synchronization' 
                : `⚠️ Out of Sync (${Math.abs(data.fullBookingCount - data.quickEventCount)} service difference)`;
            
            const resultsHTML = `
                <div class="test-panel">
                    <h3>🔄 Synchronization Status</h3>
                    <div class="sync-status ${syncStatus}">${syncMessage}</div>
                    <div class="timestamp">Last updated: ${new Date(data.timestamp).toLocaleString()}</div>
                    
                    <div class="sync-grid">
                        <div class="mode-panel full">
                            <div class="mode-title">📋 Full Booking Mode</div>
                            <div class="service-count full">${data.fullBookingCount}</div>
                            <div style="text-align: center; margin-bottom: 15px;">services available</div>
                            <div class="service-list">
                                ${data.fullBookingServices.map(service => `
                                    <div class="service-item">
                                        ${service.name} 
                                        <small>(POS: ${service.visible_on_pos}, Events: ${service.visible_on_events})</small>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        
                        <div class="mode-panel quick">
                            <div class="mode-title">⚡ Quick Event Mode</div>
                            <div class="service-count quick">${data.quickEventCount}</div>
                            <div style="text-align: center; margin-bottom: 15px;">services available</div>
                            <div class="service-list">
                                ${data.quickEventServices.map(service => `
                                    <div class="service-item">
                                        ${service.name} 
                                        <small>(POS: ${service.visible_on_pos}, Events: ${service.visible_on_events})</small>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                    
                    ${!data.isSynced ? `
                        <div class="warning">
                            <strong>⚠️ Synchronization Issue Detected</strong><br>
                            Full Booking Mode shows ${data.fullBookingCount} services while Quick Event Mode shows ${data.quickEventCount} services.
                            This indicates inconsistent filtering logic between the components.
                        </div>
                    ` : `
                        <div class="success">
                            <strong>✅ Components are synchronized</strong><br>
                            Both modes show the same ${data.fullBookingCount} services with consistent filtering.
                        </div>
                    `}
                </div>
            `;
            
            // Clear previous results and add new ones
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = resultsHTML;
        }

        function toggleAutoRefresh() {
            const autoRefresh = document.getElementById('autoRefresh');
            const refreshInterval = document.getElementById('refreshInterval');
            
            if (autoRefresh.checked) {
                const interval = parseInt(refreshInterval.value);
                autoRefreshInterval = setInterval(runSyncTest, interval);
                addResult(`🔄 Auto-refresh enabled (every ${interval/1000} seconds)`, 'info');
            } else {
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                    autoRefreshInterval = null;
                }
                addResult('⏹️ Auto-refresh disabled', 'info');
            }
        }

        function addResult(message, type = 'info') {
            const resultHTML = `<div class="${type}">${message}</div>`;
            document.getElementById('results').insertAdjacentHTML('beforeend', resultHTML);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            lastTestData = null;
        }

        function showLoading() {
            const loadingHTML = `
                <div class="test-panel loading" id="loading">
                    <div class="spinner"></div>
                    <p>Testing synchronization...</p>
                </div>
            `;
            document.getElementById('results').insertAdjacentHTML('beforeend', loadingHTML);
        }

        function hideLoading() {
            const loading = document.getElementById('loading');
            if (loading) loading.remove();
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            addResult('🌊 Real-time POS Synchronization Test Ready', 'info');
            addResult('This test simulates the filtering logic of both POS modes to verify synchronization', 'info');
            runSyncTest(); // Run initial test
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>
