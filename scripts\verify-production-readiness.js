#!/usr/bin/env node

/**
 * Production Readiness Verification Script
 * Verifies that the codebase is properly configured for production deployment
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

console.log('🔍 Verifying production readiness...\n');

let hasErrors = false;
let hasWarnings = false;

function logError(message) {
  console.log(`❌ ERROR: ${message}`);
  hasErrors = true;
}

function logWarning(message) {
  console.log(`⚠️  WARNING: ${message}`);
  hasWarnings = true;
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
}

/**
 * Check environment configuration
 */
function checkEnvironmentConfig() {
  console.log('📋 Checking environment configuration...\n');
  
  const envLocalPath = path.join(rootDir, '.env.local');
  const envProdPath = path.join(rootDir, '.env.production');
  
  if (!fs.existsSync(envLocalPath)) {
    logError('.env.local file not found');
    return;
  }
  
  const envContent = fs.readFileSync(envLocalPath, 'utf8');
  
  // Critical production checks
  const criticalChecks = [
    { pattern: /NEXT_PUBLIC_DEV_MODE=false/, name: 'Dev mode disabled', required: true },
    { pattern: /NEXT_PUBLIC_DEBUG_AUTH=false/, name: 'Debug auth disabled', required: true },
    { pattern: /ENABLE_AUTH_BYPASS=false/, name: 'Auth bypass disabled', required: true },
    { pattern: /NEXT_PUBLIC_ENABLE_AUTH_BYPASS=false/, name: 'Public auth bypass disabled', required: true },
    { pattern: /SQUARE_ENVIRONMENT=production/, name: 'Square production mode', required: true },
    { pattern: /NEXT_PUBLIC_DISABLE_CONSOLE_LOGS=true/, name: 'Console logs disabled', required: false }
  ];
  
  criticalChecks.forEach(({ pattern, name, required }) => {
    if (pattern.test(envContent)) {
      logSuccess(name);
    } else {
      if (required) {
        logError(`${name} - CRITICAL SECURITY ISSUE`);
      } else {
        logWarning(`${name} - recommended for production`);
      }
    }
  });
  
  // Check for development values that shouldn't be in production
  const devPatterns = [
    { pattern: /=true.*# For development only/, name: 'Development-only flags' },
    { pattern: /sandbox-sq0idb/, name: 'Sandbox Square credentials' },
    { pattern: /FORCE_EMAIL_IN_DEV=true/, name: 'Development email forcing' }
  ];
  
  devPatterns.forEach(({ pattern, name }) => {
    if (pattern.test(envContent)) {
      logError(`${name} found in production config`);
    }
  });
  
  if (fs.existsSync(envProdPath)) {
    logSuccess('.env.production file exists');
  } else {
    logWarning('.env.production file not found - consider creating for Vercel deployment');
  }
}

/**
 * Check for debug files that shouldn't be in production
 */
function checkDebugFiles() {
  console.log('\n🗂️  Checking for debug files...\n');
  
  const debugFiles = [
    'pages/admin/auth-debug.js',
    'pages/admin/diagnostics',
    'pages/debug',
    'pages/debug-auth.js',
    'browser-debug.js',
    'quick-error-check.js',
    'node-error-check.js',
    'public/console-monitor.js'
  ];
  
  debugFiles.forEach(file => {
    const filePath = path.join(rootDir, file);
    if (fs.existsSync(filePath)) {
      logError(`Debug file found: ${file} - should be removed for production`);
    } else {
      logSuccess(`Debug file not present: ${file}`);
    }
  });
}

/**
 * Check for console.log statements in source files
 */
function checkConsoleStatements() {
  console.log('\n🔍 Checking for console statements in source files...\n');
  
  const sourceExtensions = ['.js', '.jsx'];
  const excludeDirs = ['node_modules', '.next', '.git', 'scripts', '__tests__'];
  let consoleCount = 0;
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const relativePath = path.relative(rootDir, fullPath);
      
      // Skip excluded directories
      if (excludeDirs.some(excluded => relativePath.startsWith(excluded))) {
        return;
      }
      
      const stats = fs.statSync(fullPath);
      
      if (stats.isDirectory()) {
        scanDirectory(fullPath);
      } else if (sourceExtensions.includes(path.extname(item))) {
        scanFile(fullPath, relativePath);
      }
    });
  }
  
  function scanFile(filePath, relativePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      lines.forEach((line, index) => {
        // Skip comments and conditional console statements
        if (line.includes('console.') && 
            !line.trim().startsWith('//') && 
            !line.includes('NODE_ENV === \'development\'') &&
            !line.includes('process.env.NODE_ENV === \'development\'')) {
          consoleCount++;
          logWarning(`Console statement in ${relativePath}:${index + 1}: ${line.trim()}`);
        }
      });
    } catch (error) {
      logWarning(`Could not scan ${relativePath}: ${error.message}`);
    }
  }
  
  scanDirectory(rootDir);
  
  if (consoleCount === 0) {
    logSuccess('No unprotected console statements found');
  } else {
    logWarning(`Found ${consoleCount} console statements that may need protection`);
  }
}

/**
 * Check Next.js configuration
 */
function checkNextConfig() {
  console.log('\n⚙️  Checking Next.js configuration...\n');
  
  const nextConfigPath = path.join(rootDir, 'next.config.js');
  
  if (!fs.existsSync(nextConfigPath)) {
    logError('next.config.js not found');
    return;
  }
  
  const configContent = fs.readFileSync(nextConfigPath, 'utf8');
  
  // Check for production-ready configurations
  const configChecks = [
    { pattern: /swcMinify:\s*true/, name: 'SWC minification enabled' },
    { pattern: /reactStrictMode:\s*true/, name: 'React strict mode enabled' },
    { pattern: /Content-Security-Policy/, name: 'CSP headers configured' },
    { pattern: /X-Frame-Options/, name: 'Frame options configured' }
  ];
  
  configChecks.forEach(({ pattern, name }) => {
    if (pattern.test(configContent)) {
      logSuccess(name);
    } else {
      logWarning(`${name} not found in next.config.js`);
    }
  });
}

/**
 * Main verification
 */
function main() {
  try {
    checkEnvironmentConfig();
    checkDebugFiles();
    checkConsoleStatements();
    checkNextConfig();
    
    console.log('\n📊 Production Readiness Summary:');
    
    if (hasErrors) {
      console.log('❌ CRITICAL ISSUES FOUND - DO NOT DEPLOY TO PRODUCTION');
      console.log('   Fix all errors before deploying to production.');
      process.exit(1);
    } else if (hasWarnings) {
      console.log('⚠️  WARNINGS FOUND - Review before production deployment');
      console.log('   Consider addressing warnings for optimal production security.');
    } else {
      console.log('✅ PRODUCTION READY - All checks passed');
    }
    
    console.log('\n📋 Next steps:');
    console.log('1. Run: npm run build');
    console.log('2. Test: npm start');
    console.log('3. Deploy to production');
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    process.exit(1);
  }
}

// Run the verification
main();
